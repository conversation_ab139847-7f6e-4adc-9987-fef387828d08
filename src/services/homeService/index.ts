import { request } from '@umijs/max';

class HomeService {
  // checkText(data: API.Home.CheckParams, signal?: AbortSignal) {
  //   return request<API.Home.CheckResult>('/validate-form-correction', {
  //     method: 'post',
  //     data,
  //     name: '文本校验',
  //     signal,
  //   });
  // }

  checkText(data: API.Home.CheckParams, signal?: AbortSignal) {
    return request<API.Home.CheckResult>('/api/v1/text-check/check-text', {
      method: 'post',
      data,
      name: '文本校验',
      signal,
    });
  }

  textDetail(batch_id: string) {
    return request<API.Home.DetailResult>('/api/v1/text-check/get-check-text', {
      method: 'post',
      data: {
        batch_id
      },
      name: '获取待校验文本',
    });
  }

  replace(data: API.Home.ReplaceParams) {
    return request<API.Success>('/api/v1/text-check/replace-text', {
      method: 'post',
      data,
      name: '替换/优化文本',
    });
  }

  replaceInternal(data: API.Home.ReplaceParamsInternal) {
    return request<API.Success>('/portal/api/aiApi/ai/backFill', {
      method: 'post',
      data,
      name: '替换/优化文本',
      baseURL: "http://127.0.0.1:9999/baseApi"
    });
  }

  detection(text: string) {
    return request<API.Home.CheckResultItem>('/api/v1/text-check-single/check-single-text', {
      method: 'post',
      data: {
        text
      },
      name: '文本检测',
    });
  }
}
export default new HomeService();
