export namespace HomeType {
  export interface CheckItem {
    id: string;

    text: string;

    name?: string;

    tableName?: string;

    dbName: string;
  }
  export interface CheckParams {
    items: {
      id: string;

      text: string;
    }[]
  }

  export interface DetailResult {
    items: CheckItem[];

    selected_dbName?:string;
  }

  export interface IssuesItem {
    /**
     * 文本
     */
    text: string;

    /**
     * 错别字修改后
     */
    correction: string;

    /**
     * 位置
     */
    position: {
      /**
       * 开始位置
       */
      start: number;
      /**
       * 结束位置
       */
      end: number;
    };

    /**
     * 全局位置
     */
    global_position: {
      /**
       * 开始位置
       */
      start: number;
      /**
       * 结束位置
       */
      end: number;
    };

    /**
     * 完整句子
     */
    full_context: string;

    /**
     * 修改建议
     */
    reason: string;
  }

  export interface SegmentsItem {
    /**
     * 原始文本
     */
    original_text: string;
    /**
     * 纠正后的文本
     */
    corrected_text: string;
    /**
     * 错别字
     */
    typos: IssuesItem[];

    /**
     * 语法错误
     */
    grammar_issues: IssuesItem[];

    /**
     * 关键词建议
     */
    keyword_issues: IssuesItem[];
  }

  export interface CheckResultItem {
    id: string;

    data: CheckResultValue;
  }

  export interface CheckResultValue {
    /**
     * 错误
     */
    segments: SegmentsItem[];

    /**
     * 错误统计
     */
    summary: {
      /**
       * 总段落
       */
      total_segments: number;
      /**
       * 错别字
       */
      total_typos: number;
      /**
       * 语法建议
       */
      total_grammar_issues: number;
      /**
       * 关键词建议
       */
      total_keyword_issues: number;
      /**
       * 是否存在错误
       */
      has_errors: boolean;
    };

    /**
     * 修改后的文本
     */
    optimized_text: string;
  };

  export interface ReplaceParams extends Omit<CheckItem, 'text'> {
    /**
     * 修改后的文本
     */
    optimized_text: string;
  }

  export interface CheckResult {
    results: CheckResultItem[];
  }

  export type ReplaceParamsInternal = {
    boName?: string;

    boId: string;

    fielName?: string;

    fieldata: string;
  }[]
  
}

declare global {
  namespace API {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    export import Home = HomeType;
  }
}
