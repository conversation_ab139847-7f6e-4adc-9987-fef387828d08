declare namespace API {
  interface PageParams {
    size: number;
    page: number;
  }

  interface Result<T> {
    data: T;
  }

  type Success = null;

  // 与后端约定的响应数据格式
  interface Response {
    code: string | number;
    msg: string;
    data: any;

    // 这2个是二维码php接口格式的数据
    status: number;

    result: any;
  }

  interface DataItem extends Omit<API.Home.CheckItem, 'text'> {


    /**
     * 控制器
     */
    controller?: AbortController;

    /**
     * 原数据
     */
    originalText: string;

    /**
     * 数据
     */
    value?: Home.CheckResult['data'];

    /**
     * 生成时间
     */
    time: string;
  }

  interface QualityItem extends DataItem {
    /**
     * 对话id
     */
    conversationId:string
    text:string
    showAnalysis:boolean
    /**
     * 子项
     */
    targetControls:{
      /**
       * 类型
       */
      resultType:QualityType,

      /**
       * id
       */
      boId:string

      /**
       * 名称
       */
      boName:string

      /**
       * 字段名称
       */
      fieldName:string

      /**
       * 字段
       */
      dataContent:string
    }[]
  }

  type FileType =
    | {
      file: File;

      url: string;
    }
    | string;

    interface ItemData {
      /**
       * 内容
       */
      content: string;


      /**
       * 类型 6- content代表新的conversationId
       */
      content_type: 1 | 2 | 3 | 4 | 5 | 6 | 7
  }
}

declare namespace FormConfig {
  type Path = string | number;
}
