export namespace QualityType {
  export interface GetAnalysisRecordsParams {
    /**
     * page
     */
    page: number;

    /**
     * size
     */
    size: number;
  }

  export interface GetAnalysisRecordsItem {
    /**
     * 对话id
     */
    conversionId: string;

    /**
     * 问题
     */
    query: string

    /**
     * 用户id
     */
    userNo: string

    /**
     * id
     */
    id: string

    /**
     * 创建时间
     */
    createTime: string
  }

  export interface GetAnalysisRecordsResponse {
    /**
     * 数据
     */
    data: {
      /**
      * 总数
      */
      total: number;

      /**
       * 列表
       */
      records: GetAnalysisRecordsItem[];
    }
  }

  export interface GetAnalysisRecordDetailItem {
    /**
     * 
     */
    id: string;

    /**
     * 内容
     */
    content: string;

    /**
     * 创建时间
     */
    create_time: string

    /**
     * 类型
     */
    role: 0 | 1 | 2 | 3
  }

  export interface GetAnalysisRecordDetailResponse {
    data: {
      /**
     * id
     */
      id: string;

      /**
       * 表格信息
       */
      records: GetAnalysisRecordDetailItem[];
    }
  }
}
declare global {
  declare namespace API {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    export import Quality = QualityType;
  }
}
