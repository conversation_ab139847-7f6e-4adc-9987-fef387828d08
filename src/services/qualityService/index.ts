import { request } from '@umijs/max';

class QualityService {
  getAnalysisRecords(data: API.Quality.GetAnalysisRecordsParams) {
    return request<API.Quality.GetAnalysisRecordsResponse>(
      '/api/v1/analysis/analysis-records',
      {
        method: 'get',
        data,
        name: '获取分析记录',
      },
    );
  }
  deleteAnalysisRecord(conversionId: string) {
    return request<API.Success>(
      `/api/v1/analysis/analysis-records/${conversionId}`,
      {
        method: 'delete',
        name: '删除分析记录',
      },
    );
  }
  clearAnalysisRecord() {
    return request<API.Success>(
      "/api/v1/analysis/analysis-records/clear-all",
      {
        method: 'delete',
        name: '清空分析记录',
      },
    );
  }
  getAnalysisRecordDetail(conversionId: string) {
    return request<API.Quality.GetAnalysisRecordDetailResponse>(
      `/api/v1/analysis/analysis-records/${conversionId}`,
      {
        method: 'get',
        name: '获取分析记录详情',
      },
    );
  }
}

export default new QualityService();
