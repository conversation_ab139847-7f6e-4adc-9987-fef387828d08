import type { RequestConfig, RequestOptions } from '@umijs/max';
import { md5 } from "js-md5"
import useStore from './store';

export const request: RequestConfig = {
  timeout: 1000000,
  baseURL: process.env.baseURL,
  headers: {
    'Content-Type': 'application/json',
  },
  errorConfig: {
    errorHandler() {
    },
    errorThrower() {
    }
  },
  requestInterceptors: [
    (config: RequestOptions) => {
      const Timestamp = new Date().getTime();
      const useInfo = useStore.getState().useInfo;
      return {
        ...config,
        headers: {
          Timestamp,
          Authorization: md5('lFKFdCvh0e0GEt' + Timestamp),
          userNo: useInfo?.userId,
          userName: encodeURIComponent(useInfo?.userName),
          securityLevel: useInfo?.securityLevel,
          sessionId: useInfo?.sessionId,
        },
      };
    },
  ],
  responseInterceptors: [(response: any) => {
    // 拦截响应数据，进行个性化处理
    if (response?.data?.success === false) {
      return Promise.reject(response);
    }
    return response;
  }]
};