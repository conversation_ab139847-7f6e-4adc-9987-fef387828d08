/**
 * 打字机回答类型
 */
export type AnswerType = 'thinking' | 'answer';

/**
 * 段落类型
 */
export interface IParagraph {
  content: string;                      // 段落内容
  isTyped: boolean;                     // 是否已完成打字
  type: 'text' | 'br';                  // 段落类型，文本或换行
  answerType: AnswerType;               // 回答类型
  tokensReference: Record<number, {     // 标记引用
    startIndex: number;                 // 开始索引
    raw: string;                        // 原始内容
  }>;
}

/**
 * Markdown打字机组件属性
 */
export interface MarkdownTypingProps {
  interval?: number;                     // 打字间隔(毫秒)
  onEnd?: (data: {                       // 打字结束回调
    str?: string;                       
    answerType?: AnswerType;            
  }) => void;                           
  onStart?: (data: {                    // 打字开始回调
    currentIndex: number;               
    currentChar: string;                
    answerType: AnswerType;             
    prevStr: string;                    
  }) => void;                          
  onTypedChar?: (data: {               // 打字过程回调
    currentIndex: number;              
    currentChar: string;               
    answerType: AnswerType;            
    prevStr: string;                   
  }) => void;                           
  isClosePrettyTyped?: boolean;         // 是否关闭优化打字效果
  defaultSpeed?: number;                // 默认打字速度
  thinkingContent?: string;             // 思考内容
  answerContent?: string;               // 回答内容
}

/**
 * Markdown打字机组件引用方法
 */
export interface MarkdownTypingRef {
  push: (content: string, answerType: AnswerType) => void;  // 添加内容
  clear: () => void;                                        // 清空内容
  triggerWholeEnd: () => void;                              // 触发结束
} 