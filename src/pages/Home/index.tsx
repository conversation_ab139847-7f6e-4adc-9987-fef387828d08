import homeService from '@/services/homeService';
import { useParams } from '@umijs/max';
import { useDynamicList, useMemoizedFn, useRequest } from 'ahooks';
import type { TabsProps } from 'antd';
import { Spin, Tabs, message } from 'antd';
import dayjs from 'dayjs';
import React, { useMemo, useState } from 'react';
import RenderSegments from './components/RenderSegments';

const Home: React.FC = () => {
  const { list, resetList, replace } = useDynamicList<API.DataItem>([]);

  const [activeKey, setActiveKey] = useState<string>();

  const [isError, setIsError] = useState(false);

  const ready = useMemo(() => {
    return list.length > 0;
  }, [list]);

  const [messageApi, contextMessageHolder] = message.useMessage();

  const { id } = useParams();

  // 重新分析后-更新列表中的质量问题分析的对话id
  const onUpdateConversationId = useMemoizedFn((item: API.QualityItem) => {
    const index = list.findIndex((val) => val.id === item.id);
    replace(index, item);
  });

  const { loading } = useRequest(
    async () => {
      // const controller = new AbortController();
      const result = await homeService.checkText(
        {
          items: list.map((val) => ({
            id: val.dbName,
            text: val.originalText,
          })),
        },
        // controller.signal,
      );
      return result;
    },
    {
      onSuccess: (res) => {
        const newList = list.map((val) => {
          const value = res.results.find(
            (item) => item.id === val.dbName,
          )?.data;
          return {
            ...val,
            value,
          };
        });
        resetList(newList);
      },
      onError: (err: any) => {
        messageApi.error(err.response?.data?.detail);
        setIsError(true);
      },
      ready,
    },
  );

  const items: TabsProps['items'] = useMemo(() => {
    return list.map((val) => {
      return {
        key: val.dbName,
        label: val.name,
        children: (
          <RenderSegments
            item={val}
            loading={loading}
            disabled={isError}
            onUpdateConversationId={onUpdateConversationId}
          />
        ),
      };
    });
  }, [isError, list, loading, onUpdateConversationId]);

  const { loading: detailLoading } = useRequest(
    async () => {
      return homeService.textDetail(id!);
    },
    {
      ready: Boolean(id),
      onSuccess: (res) => {
        resetList(
          res.items?.map((val) => ({
            ...val,
            originalText: val.text,
            value: undefined,
            time: dayjs().format('MM-DD HH:mm:ss'),
          })),
        );
        setActiveKey(res.selected_dbName ?? res.items?.[0].dbName);
      },
    },
  );

  return (
    <div className="relative">
      {contextMessageHolder}
      {/* <div className="flex items-center justify-between px-[16px] py-[8px] border-b border-gray-100">
        <div className="flex items-center gap-2">
          <span className="text-[16px] font-medium">文本纠错</span>
          <Tooltip title="原文错误内容将以中划线标注，点击替换一键修改所有错误（如无需修改错误点可点击高亮色块继续使用原文内容）">
            <QuestionCircleOutlined className="text-gray-400 cursor-pointer" />
          </Tooltip>
        </div>
      </div> */}
      <Spin spinning={detailLoading} className="">
        <div className="min-h-[100px] px-6 py-2">
          {ready && (
            <Tabs activeKey={activeKey} items={items} onChange={setActiveKey} />
          )}
        </div>
        {/* {!ready && (
          <div className="p-3 space-y-3 bg-[#fff] mt-[49px]">
            <Empty description="暂无数据" />
          </div>
        )} */}
      </Spin>
    </div>
  );
};
export default Home;
