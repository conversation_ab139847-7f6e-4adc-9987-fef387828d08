import titleIcon from '@/assets/titleIcon.png';
import { CheckType } from '@/enum';
import useClipboard from '@/hooks/useClipboard';
import homeService from '@/services/homeService';
import type { ReplaceTextResult } from '@/utils';
import { LoadingOutlined, WarningOutlined } from '@ant-design/icons';
import { useMemoizedFn } from 'ahooks';
import { Button, Input, Modal, Tabs, message } from 'antd';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import CustomScrollbars from 'react-custom-scrollbars';
import Quality from './components/Quality';
import { formatCheckList } from './utils';

const { TextArea } = Input;

interface IProps {
  loading?: boolean;

  item: API.DataItem;

  disabled: boolean;

  // 重新分析后-更新列表中的质量问题分析的对话id
  onUpdateConversationId: (item: API.QualityItem) => void;
}

const colorMap = {
  [CheckType.TYPO]: '#ff898a',
  [CheckType.GRAMMAR]: '#FAAD14',
  [CheckType.KEYWORD]: '#3388FF',
};

const bgColorMap = {
  [CheckType.TYPO]: '#ffd3d4',
  [CheckType.GRAMMAR]: '#FFEEB6',
  [CheckType.KEYWORD]: '#D5E7FF',
};

function RenderSegments(props: IProps) {
  const { loading, item, disabled, onUpdateConversationId } = props;
  const {
    value: detectionResult,
    originalText: originalTextValue,
    id,
    tableName,
    dbName,
    name,
  } = item;
  const [data, setData] = useState<ReplaceTextResult[]>([]);
  const { onCopyText } = useClipboard();
  const [text, setText] = useState<string>('');
  const textAreaRef = useRef<HTMLTextAreaElement>(null);

  const [value, setValue] = useState<any>();

  const [singleLoading, setSingleLoading] = useState(false);

  const [originalText, setOriginalText] = useState(originalTextValue);

  const [modal, contextHolder] = Modal.useModal();
  const [messageApi, contextMessageHolder] = message.useMessage();

  const nowText = useMemo(() => {
    return data.map((item) => item.text || item.old).join('') || originalText;
  }, [data, originalText]);

  const loadingValue = useMemo(() => {
    return loading || singleLoading;
  }, [loading, singleLoading]);

  const onHandleClick = useCallback(
    (item: ReplaceTextResult) => {
      // 替换new和old
      setData((draft) => {
        return draft?.map((cItem) => {
          if (cItem.id === item.id) {
            // return { ...cItem, new: item.old, old: item.new };
            const newText = cItem.text === item.new ? item.old : item.new;
            return {
              ...cItem,
              text: newText,
            };
          }
          return cItem;
        }) as ReplaceTextResult[];
      });
    },
    [setData],
  );

  const onHandleCopyText = useMemoizedFn(async () => {
    try {
      await onCopyText(text);
      messageApi.success('复制成功');
    } catch (error) {
      messageApi.error('复制失败');
    }
  });

  useEffect(() => {
    setValue(detectionResult);
  }, [detectionResult]);

  useEffect(() => {
    if (value) {
      const newData = formatCheckList(value);
      setData(newData);
    }
  }, [value]);

  // 采纳
  const onAdopt = () => {
    setText(nowText);
    textAreaRef.current?.focus();
  };

  // 原文检测
  const onDetection = async (text?: string) => {
    setSingleLoading(true);
    try {
      const value = text ?? originalText;
      setOriginalText(value);
      const res = await homeService.detection(value);
      setValue(res.data);
    } catch (error: any) {
      messageApi.error(error.response?.data?.detail);
    } finally {
      setSingleLoading(false);
    }
  };

  // const onHandleStop = useCallback(() => {
  //   controller?.abort();
  // }, [controller]);

  const onHandleReplace = useMemoizedFn(async () => {
    modal.confirm({
      title: '是否确认替换？',
      onOk: async () => {
        try {
          const isInternal = process.env.internal === '1';
          if (isInternal) {
            await homeService.replaceInternal([
              {
                boName: tableName,
                boId: id,
                fielName: dbName,
                fieldata: text,
              },
            ]);
            onDetection(text);
            messageApi.success('替换成功');
          } else {
            await homeService.replace({
              id,
              optimized_text: text,
              tableName,
              name,
              dbName,
            });
            onDetection(text);
            messageApi.success('替换成功');
          }
        } catch (err: any) {
          // messageApi.error('替换失败');
          messageApi.error(err.response?.data?.detail);
          return Promise.reject();
        }
      },
    });
  });

  const tipsInfo = useMemo(() => {
    const typo = value?.summary.total_typos ?? 0;
    const grammar = value?.summary.total_grammar_issues ?? 0;
    const keyword = value?.summary.total_keyword_issues ?? 0;
    return {
      total: typo + grammar + keyword,
      typo,
      grammar,
      keyword,
    };
  }, [value]);

  return (
    <React.Fragment>
      <div className="mt-5 text-[15px]">
        {contextHolder}
        {contextMessageHolder}
        <Tabs
          type="card"
          className="w-full tabs"
          items={[
            {
              key: '1',
              label: '文本纠错',
              children: (
                <div className="bg-white px-4 py-5 rounded-[0_8px_8px_8px]">
                  {/* <div className="flex items-center gap-x-2 pb-3">
                    <img
                      className="size-[28px]"
                      src={titleIcon}
                      alt="titleIcon"
                    />
                    <span className="text-[rgba(0,0,0,0.75)] text-[15px] font-[600]">
                      纠错提示
                    </span>
                  </div> */}
                  <CustomScrollbars
                    className="bg-[#F5F7FD] rounded-md overflow-y-auto text-[15px]"
                    autoHeight
                    autoHeightMin={150}
                    autoHeightMax={220}
                  >
                    <div className="px-4 py-2">
                      {loadingValue ? (
                        <div className="text-[15px] text-gray-500 mb-2">
                          {originalText}
                        </div>
                      ) : (
                        <React.Fragment>
                          {data.map((item, index) => {
                            return (
                              <span key={index}>
                                {item.new && (
                                  <span
                                    className="px-[4px] mx-[2px] rounded-[4px]"
                                    // item.real === item.new 代表原文 原文黑色字体加背景
                                    style={{
                                      backgroundColor:
                                        item.real === item.new
                                          ? bgColorMap[item.type]
                                          : undefined,
                                      color:
                                        item.real === item.new
                                          ? 'rgba(0,0,0,.75)'
                                          : colorMap[item.type],
                                      cursor: Boolean(item.new)
                                        ? 'pointer'
                                        : 'auto',
                                      textDecoration:
                                        item.new !== item.text
                                          ? 'line-through'
                                          : 'none',
                                      // textDecoration:
                                      // item.real === item.new ? 'line-through' : 'none',
                                    }}
                                    onClick={() => onHandleClick(item)}
                                  >
                                    {item.new}
                                  </span>
                                )}
                                <span
                                  // item.real === item.old 代表原文 或者Item.new等于空也代表原文没有错别内容 黑色字体加背景 h
                                  style={{
                                    color:
                                      !Boolean(item.new) ||
                                      item.real === item.old
                                        ? 'rgba(0,0,0,.75)'
                                        : colorMap[item.type],
                                    backgroundColor:
                                      item.real === item.old
                                        ? bgColorMap[item.type]
                                        : undefined,
                                    textDecoration:
                                      item.old !== item.text && item.text
                                        ? 'line-through'
                                        : 'none',
                                    cursor: Boolean(item.text)
                                      ? 'pointer'
                                      : 'auto',
                                  }}
                                  onClick={() => {
                                    if (item.text) {
                                      onHandleClick(item);
                                    }
                                  }}
                                >
                                  {item.old}
                                </span>
                              </span>
                            );
                          })}
                        </React.Fragment>
                      )}
                    </div>
                  </CustomScrollbars>
                  {loadingValue ? (
                    <div className="flex flex-col">
                      <div className="flex items-center justify-between text-sm mt-5">
                        <div className="flex items-center space-x-2">
                          <LoadingOutlined className="text-[#1677ff]" />
                          <span className="text-[#1677ff] text-[15px]">
                            AI 检测中...
                          </span>
                        </div>
                      </div>
                      <div className="relative overflow-hidden h-[3px] rounded-full mt-2">
                        <div className="absolute inset-0 bg-gradient-to-r from-[rgba(22,119,255,0.7)] to-[rgba(22,119,255,0.4] animate-loading-bar" />
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center justify-between py-4">
                      {tipsInfo.total > 0 ? (
                        <div className="text-sm flex items-center text-gray-600 leading-6">
                          <WarningOutlined className="text-[#FAAD14] mr-2 text-[18px] relative" />
                          共有 {tipsInfo.total} 处错误，
                          <span className="text-[#ff898a] mr-[2px]">
                            {tipsInfo.typo}
                          </span>
                          处错别字，
                          <span className="text-[#FAAD14] mr-[2px]">
                            {tipsInfo.grammar}
                          </span>
                          处语法错误，
                          <span className="text-[#3388FF] mr-[2px]">
                            {tipsInfo.keyword}
                          </span>
                          处关键词建议。
                        </div>
                      ) : (
                        <div className="text-sm px-[11px] text-gray-500">
                          无文本错误
                        </div>
                      )}
                      <div>
                        <Button
                          disabled={disabled}
                          size="middle"
                          onClick={() => onDetection(originalText)}
                        >
                          检测
                        </Button>
                        <Button
                          disabled={disabled}
                          type="primary"
                          className="ml-3"
                          size="middle"
                          onClick={onAdopt}
                        >
                          采纳
                        </Button>
                      </div>
                    </div>
                  )}
                  <div
                    className={`flex items-center gap-x-2 pb-3 ${
                      loadingValue ? 'mt-4' : 'mt-1'
                    }`}
                  >
                    <img
                      className="size-[28px]"
                      src={titleIcon}
                      alt="titleIcon"
                    />
                    <span className="text-[rgba(0,0,0,0.75)] text-[15px] font-[600]">
                      采纳结果
                    </span>
                  </div>
                  <TextArea
                    rows={6}
                    className="!rounded-md px-4 py-2 !text-[15px] text-[rgba(0,0,0,.75)]"
                    placeholder="待采纳"
                    value={text}
                    ref={textAreaRef}
                    onChange={(e) => setText(e.target.value)}
                  />
                  <div className="flex justify-end mt-4">
                    <Button
                      disabled={disabled}
                      type="default"
                      onClick={onHandleCopyText}
                    >
                      复制
                    </Button>
                    <Button
                      disabled={disabled}
                      className="ml-3"
                      type="primary"
                      onClick={onHandleReplace}
                    >
                      替换
                    </Button>
                  </div>
                </div>
              ),
            },
            ...((item as API.QualityItem)?.showAnalysis
              ? [
                  {
                    key: '2',
                    label: '辅助分析',
                    children: (
                      <Quality
                        onUpdateConversationId={onUpdateConversationId}
                        item={item as API.QualityItem}
                      />
                    ),
                  },
                ]
              : []),
            {
              key: '3',
              label: '自动生成',
              disabled: true,
            },
          ]}
        />
      </div>

      <style>{`
        @keyframes loadingBar {
          0% {
            transform: translateX(-100%);
          }
          100% {
            transform: translateX(100%);
          }
        }
        
        .animate-loading-bar {
          animation: loadingBar 1.5s ease-in-out infinite;
        }
      `}</style>
    </React.Fragment>
  );
}

export default RenderSegments;
