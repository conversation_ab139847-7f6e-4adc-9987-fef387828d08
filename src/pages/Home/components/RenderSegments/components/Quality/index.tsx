import {
  AlertTwoTone,
  LoadingOutlined,
  QuestionCircleTwoTone,
  WarningTwoTone,
} from '@ant-design/icons';
import { Button, message, Modal } from 'antd';
import CustomScrollbars from 'react-custom-scrollbars';
import Markdown from 'react-markdown';
// remark-gfm 是一个 remark 插件 如表格、任务列表、自动链接等
import remarkGfm from 'remark-gfm';
// rehype-raw 是一个 rehype 插件 用于处理 Markdown 中的原始 HTML 内容
import useClipboard from '@/hooks/useClipboard';
import homeService from '@/services/homeService';
import { useBoolean, useMemoizedFn, useMount } from 'ahooks';
import 'github-markdown-css/github-markdown-light.css';
import { useCallback, useRef, useState } from 'react';
import rehypeRaw from 'rehype-raw';
import useStore from '@/store';
import { fetchEventSource } from '@microsoft/fetch-event-source';

enum QualityType {
  ANALYSIS = 'analysis',
  MEASURE = 'measure',
}

export interface IProps {
  item: API.QualityItem;

  onUpdateConversationId: (item: API.QualityItem) => void;
}

// 使用@microsoft/fetch-event-source实现流处理
const Request = {
  create: async (params: { query: string; fieldId: string; stream: boolean }, options: {
    onStream?: (controller: AbortController) => void;
    onSuccess?: (messages: any) => void;
    onError?: (error: Error) => void;
    onUpdate?: (msg: { data: string }) => void;
  }) => {
    const useInfo = useStore.getState().useInfo;
    const baseURL = process.env.baseURL || '';
    const url = `${baseURL}/api/v1/analysis/analyze-stream-v2?query=${params.query}&fieldId=${params.fieldId}`;
    
    // 创建AbortController
    const controller = new AbortController();
    if (options.onStream) {
      options.onStream(controller);
    }
    
    try {
      await fetchEventSource(url, {
        method: 'GET',
        headers: {
          'Accept': 'text/event-stream',
          'userNo': useInfo?.userId || '',
          'userName': encodeURIComponent(useInfo?.userName || ''),
          'securityLevel': useInfo?.securityLevel || '',
          'sessionId': useInfo?.sessionId || '',
        },
        signal: controller.signal,
        onmessage: (event) => {
          if (options.onUpdate) {
            options.onUpdate({ data: event.data });
          }
        },
        onopen: async (response) => {
          if (response.ok) {
            return; // 连接成功
          } else if (response.status >= 400 && response.status < 500) {
            // 客户端错误
            if (options.onError) {
              options.onError(new Error(`客户端错误: ${response.status}`));
            }
            controller.abort();
          }
        },
        onclose: () => {
          // 连接关闭后执行onSuccess回调
          if (options.onSuccess) {
            options.onSuccess([]);
          }
        },
        onerror: (err) => {
          // 错误处理
          if (options.onError) {
            options.onError(err instanceof Error ? err : new Error('未知错误'));
          }
          // fetchEventSource的onerror应该返回void或undefined
          return;
        }
      });
    } catch (error) {
      if (options.onError && error instanceof Error) {
        options.onError(error);
      }
    }
  }
};

function Quality(props: IProps) {
  const { item, onUpdateConversationId } = props;

  const [analysis, setAnalysis] = useState<string | null>(''); // 分析
  const [answer, setAnswer] = useState<string | null>(''); // 落实

  const abortController = useRef<AbortController>();

  const [modal, contextHolder] = Modal.useModal();

  const [loading, { setFalse, setTrue }] = useBoolean(false);

  const { onCopyText } = useClipboard();

  const [messageApi, contextMessageHolder] = message.useMessage();

  const onHandleReplace = useMemoizedFn(async (type: QualityType) => {
    modal.confirm({
      title: '是否确认替换？',
      onOk: async () => {
        const optimized_text =
          type === QualityType.ANALYSIS ? analysis : answer;
        const info = item.targetControls?.find((c) => c.resultType === type);
        if (info) {
          try {
            const isInternal = process.env.internal === '1';
            if (isInternal) {
              await homeService.replaceInternal([
                {
                  boName: info?.boName,
                  boId: info?.boId,
                  fielName: info?.fieldName,
                  fieldata: optimized_text || '',
                },
              ]);
              messageApi.success('替换成功');
            } else {
              await homeService.replace({
                id: info?.boId,
                optimized_text: optimized_text || '',
                tableName: info?.boName,
                name: info?.boName,
                dbName: info?.fieldName,
              });
              messageApi.success('替换成功');
            }
          } catch (err: any) {
            // messageApi.error('替换失败');
            messageApi.error(err.response?.data?.detail);
            return Promise.reject();
          }
        } else {
          message.warning('无详情数据');
        }
      },
    });
  });

  const onHandleCopyText = useMemoizedFn(async (text: string) => {
    try {
      await onCopyText(text);
      message.success('复制成功');
    } catch (error) {
      message.error('复制失败');
    }
  });

  const onInitValue = useCallback(() => {
    setAnalysis('');
    setAnswer('');
    // 取消请求
    if (abortController.current) {
      abortController.current.abort();
    }
  }, []);

  const onAnalyze = async () => {
    if (!item.text) {
      message.info('请输入问题描述');
      return;
    }
    onInitValue();
    setTrue();
    await Request.create(
      {
        query: item.text || '',
        fieldId: item.id,
        stream: true,
      },
      {
        onStream: (controller: AbortController) => {
          abortController.current = controller;
        },
        onSuccess: (messages: any) => {
          console.log('onSuccess', messages);
          setFalse();
        },
        onError: (error: Error) => {
          console.error('onError', error);
          message.error('接口请求失败，请稍后重试');
          setFalse();
        },
        onUpdate: (msg: { data: string }) => {
          if (loading) {
            setFalse();
          }
          try {
            const data: API.ItemData = JSON.parse(msg.data);
            // 根据 content_type 分类追加内容
            if (data.content_type === 4) {
              setAnalysis((prev) => prev + data.content);
            } else if (data.content_type === 5) {
              setAnswer((prev) => prev + data.content);
            } else if (data.content_type === 6) {
              onUpdateConversationId({
                ...item,
                conversationId: data.content,
              });
            }
          } catch (error) {}
        },
      },
    );
  };

  const initDetail = useCallback(() => {
    const analysisInfo = item.targetControls?.find(
      (c) => c.resultType === QualityType.ANALYSIS,
    );
    const measureInfo = item.targetControls?.find(
      (c) => c.resultType === QualityType.MEASURE,
    );
    if (analysisInfo) {
      setAnalysis(analysisInfo.dataContent);
    }
    if (measureInfo) {
      setAnswer(measureInfo.dataContent);
    }
  }, [item]);

  const onHandleDetail = useMemoizedFn(() => {
    window.open(`./#/quality?id=${item.conversationId}`, '_blank');
  });

  useMount(() => {
    const isGetAnsy = item?.targetControls?.every((item) => item.dataContent);
    if (isGetAnsy) {
      initDetail();
    } else {
      onAnalyze();
    }
  });

  return (
    <div className="bg-white w-full p-[16px] markdown-body rounded-[0_8px_8px_8px]">
      {contextHolder}
      {contextMessageHolder}
      <div className="flex flex-col">
        <div className="flex items-center gap-x-2 pb-3 mt-1">
          <QuestionCircleTwoTone />
          <span className="text-[rgba(0,0,0,0.75)] text-[15px] font-[600]">
            问题描述
          </span>
        </div>
        <CustomScrollbars
          className="bg-[#F5F7FD] rounded-md overflow-y-auto text-[15px]"
          autoHeight
          autoHeightMin={150}
          autoHeightMax={220}
        >
          <div className="px-4 py-2">{item.text}</div>
        </CustomScrollbars>
      </div>
      {!loading && (
        <div className="flex justify-end gap-4 mt-4">
          <Button onClick={onAnalyze} loading={loading}>
            重新分析
          </Button>
          <Button type="primary" onClick={onHandleDetail}>
            查看详情
          </Button>
        </div>
      )}
      {loading && (
        <div className="flex flex-col">
          <div className="flex items-center justify-between text-sm mt-5">
            <div className="flex items-center space-x-2">
              <LoadingOutlined className="text-[#1677ff]" />
              <span className="text-[#1677ff] text-[15px]">AI 分析中...</span>
            </div>
          </div>
          <div className="relative overflow-hidden h-[3px] rounded-full mt-2">
            <div className="absolute inset-0 bg-gradient-to-r from-[rgba(22,119,255,0.7)] to-[rgba(22,119,255,0.4] animate-loading-bar" />
          </div>
        </div>
      )}
      <div className="flex flex-col">
        <div
          className={`flex items-center gap-x-2 pb-3 ${
            loading ? 'mt-4' : 'mt-1'
          }`}
        >
          <WarningTwoTone />
          <span className="text-[rgba(0,0,0,0.75)] text-[15px] font-[600]">
            定位及问题原因
          </span>
        </div>
        <CustomScrollbars
          className="bg-[#F5F7FD] rounded-md overflow-y-auto text-[15px]"
          autoHeight
          autoHeightMin={150}
        >
          <div className="px-4 py-2">
            <Markdown remarkPlugins={[remarkGfm]} rehypePlugins={[rehypeRaw]}>
              {analysis}
            </Markdown>
          </div>
        </CustomScrollbars>
        <div className="flex justify-end gap-4 mt-4">
          <Button onClick={() => onHandleCopyText(analysis || '')}>复制</Button>
          <Button
            type="primary"
            disabled={!analysis}
            onClick={() => onHandleReplace(QualityType['ANALYSIS'])}
          >
            采纳
          </Button>
        </div>
      </div>
      <div className="flex flex-col">
        <div className="flex items-center gap-x-2 pb-3 mt-1">
          <AlertTwoTone />
          <span className="text-[rgba(0,0,0,0.75)] text-[15px] font-[600]">
            措施及落实情况
          </span>
        </div>
        <CustomScrollbars
          className="bg-[#F5F7FD] rounded-md overflow-y-auto text-[15px]"
          autoHeight
          autoHeightMin={150}
        >
          <div className="px-4 py-2">
            <Markdown remarkPlugins={[remarkGfm]} rehypePlugins={[rehypeRaw]}>
              {answer}
            </Markdown>
          </div>
        </CustomScrollbars>
        <div className="flex justify-end gap-4 mt-4">
          <Button onClick={() => onHandleCopyText(answer || '')}>复制</Button>
          <Button
            type="primary"
            disabled={!answer}
            onClick={() => onHandleReplace(QualityType['MEASURE'])}
          >
            采纳
          </Button>
        </div>
      </div>
    </div>
  );
}

export default Quality;
