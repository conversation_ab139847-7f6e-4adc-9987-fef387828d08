import { CheckType } from '@/enum';
import { replaceText } from '@/utils';

export function formatCheckList(value: API.Home.CheckResultValue) {
  const list = value.segments.map((item) => {
    const typos = item.typos?.map((cItem) => {
      return {
        ...cItem,
        type: CheckType.TYPO,
      };
    });
    const grammarIssues = item.grammar_issues?.map((gItem) => {
      return {
        ...gItem,
        type: CheckType.GRAMMAR,
      };
    });
    const keywordIssues = item.keyword_issues?.map((kItem) => {
      return {
        ...kItem,
        type: CheckType.KEYWORD,
      };
    });
    const checkList = [...typos, ...grammarIssues, ...keywordIssues]?.map(
      (item) => {
        return {
          text: item.text,
          correction: item.correction,
          type: item.type,
          start: item.position.start,
          end: item.position.end,
        };
      },
    );
    return replaceText(item.original_text, checkList);
  });
  return list.flat();
}
