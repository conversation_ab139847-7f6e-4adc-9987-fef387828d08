import {
  ClockCircleOutlined,
  DashboardFilled,
  QuestionCircleFilled,
  SearchOutlined,
} from '@ant-design/icons';
// import { XRequest } from '@ant-design/x';
import type { FormInstance } from 'antd';
import { Button, Collapse, Form, Input, message, Spin } from 'antd';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
// remark-gfm 是一个 remark 插件 如表格、任务列表、自动链接等
import remarkGfm from 'remark-gfm';
// rehype-raw 是一个 rehype 插件 用于处理 Markdown 中的原始 HTML 内容
import emptyImage from '@/assets/empty.png';
import PdfIcon from '@/assets/pdf.png';
import LoadingDots from '@/components/LoadingDots';
import { useAutoScroll } from '@/hooks/useAutoScroll';
import useVisible from '@/hooks/useVisible';
import qualityService from '@/services/qualityService';
import useStore from '@/store';
import { useLocation } from '@umijs/max';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { useBoolean, useMount } from 'ahooks';
import 'github-markdown-css/github-markdown-light.css';
import queryString from 'query-string';
import { Else, If, Then } from 'react-if';
import Markdown from 'react-markdown';
import rehypeRaw from 'rehype-raw';
import History from './components/History';
import Reference from './components/Reference';

// 使用@microsoft/fetch-event-source实现流处理
const Request = {
  create: async (params: { query: string; stream: boolean }, options: {
    onStream?: (controller: AbortController) => void;
    onSuccess?: (messages: any) => void;
    onError?: (error: Error) => void;
    onUpdate?: (msg: { data: string }) => void;
  }) => {
    const useInfo = useStore.getState().useInfo;
    const baseURL = process.env.baseURL || '';
    const url = `${baseURL}/api/v1/analysis/analyze-stream-v2?query=${params.query}`;
    
    // 创建AbortController
    const controller = new AbortController();
    if (options.onStream) {
      options.onStream(controller);
    }
    
    try {
      await fetchEventSource(url, {
        method: 'GET',
        headers: {
          'Accept': 'text/event-stream',
          'userNo': useInfo?.userId || '',
          'userName': encodeURIComponent(useInfo?.userName || ''),
          'securityLevel': useInfo?.securityLevel || '',
          'sessionId': useInfo?.sessionId || '',
        },
        signal: controller.signal,
        onmessage: (event) => {
          if (options.onUpdate) {
            options.onUpdate({ data: event.data });
          }
        },
        onopen: async (response) => {
          if (response.ok) {
            return; // 连接成功
          } else if (response.status >= 400 && response.status < 500) {
            // 客户端错误
            if (options.onError) {
              options.onError(new Error(`客户端错误: ${response.status}`));
            }
            controller.abort();
          }
        },
        onclose: () => {
          // 连接关闭后执行onSuccess回调
          if (options.onSuccess) {
            options.onSuccess([]);
          }
        },
        onerror: (err) => {
          // 错误处理
          if (options.onError) {
            options.onError(err instanceof Error ? err : new Error('未知错误'));
          }
          // fetchEventSource的onerror应该返回void或undefined，而不是boolean
          return;
        }
      });
    } catch (error) {
      if (options.onError && error instanceof Error) {
        options.onError(error);
      }
    }
  }
};

export default function HomePage() {
  const formRef = useRef<FormInstance<{ desc: string }>>(null);

  // 新增：为每个面板维护内容
  const [knowledge, setKnowledge] = useState<string | null>(''); // 知识库检索
  const [thinking, setThinking] = useState<string | null>(''); // 思考
  const [answer, setAnswer] = useState<string | null>(''); // 回答
  // 参考文献
  const [reference, setReference] = useState<{
    url:string
    fileName:string
  }>({
    url: '',
    fileName: '',
  });

  const [referenceVisible, referenceActions] = useVisible(false);

  const [detailLoading, setDetailLoading] = useState(false);

  const location = useLocation();

  // 路由传参
  const { query, id } = queryString.parse(location.search);

  const [historyVisible, historyActions] = useVisible(false);

  const [loading, { setFalse, setTrue }] = useBoolean(false);

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [thinkingActiveKey, setThinkingActiveKey] = useState<string[]>([
    'default',
  ]);

  const [messageApi, contextHolder] = message.useMessage();

  // 当前正在进行到的类型
  const [currentType, setCurrentType] = useState<
    API.ItemData['content_type'] | 0
  >(0);

  // 取消请求
  const abortController = useRef<AbortController>();

  const empty = useMemo(() => {
    return knowledge === '' && thinking === '' && answer === '' && !loading;
  }, [knowledge, thinking, answer, loading]);

  const onInitValue = useCallback(() => {
    setKnowledge('');
    setThinking('');
    setAnswer('');
    setThinkingActiveKey(['default']);
    // 取消请求
    if (abortController.current) {
      abortController.current.abort();
    }
  }, []);

  const { domRef, onScroll, tryAutoScroll } = useAutoScroll<HTMLDivElement>();

  // 智能分析
  const onAnalyze = async () => {
    const values = await formRef.current?.validateFields();
    onInitValue();
    setTrue();
    await Request.create(
      {
        query: values?.desc || '',
        stream: true,
      },
      {
        onStream: (controller: AbortController) => {
          abortController.current = controller;
        },
        onSuccess: (messages: any) => {
          console.log('onSuccess', messages);
          setFalse();
        },
        onError: (error: Error) => {
          console.error('onError', error);
          messageApi.error('接口请求失败，请稍后重试');
          setFalse();
        },
        onUpdate: (msg: { data: string }) => {
          if (loading) {
            setFalse();
          }
          try {
            const data: API.ItemData = JSON.parse(msg.data);
            // 根据 content_type 分类追加内容
            setCurrentType(data.content_type);
            if (data.content_type === 1) {
              setKnowledge((prev) => prev + data.content);
            } else if (data.content_type === 2) {
              setThinking((prev) => prev + data.content);
            } else if (data.content_type === 3) {
              setAnswer((prev) => prev + data.content);
            } else if (data.content_type === 7) {
              const jsonInfo = JSON.parse(data.content);
              setReference(() => ({
                url: jsonInfo.url,
                fileName: jsonInfo.fileName,
              }));
            }
            setTimeout(() => {
              tryAutoScroll();
            }, 0);
          } catch (error) {}
        },
      },
    );
  };

  useEffect(() => {
    if (currentType === 3) {
      // 进行到回答阶段 折叠关闭思考面板
      setThinkingActiveKey([]);
    }
  }, [currentType]);

  const onHandleSelect = useCallback(
    async (conversionId: string) => {
      historyActions.setFalse();
      setDetailLoading(true);
      const result = await qualityService.getAnalysisRecordDetail(conversionId);
      const data = result.data;
      const queryInfo = data?.records.find(
        (c: API.Quality.GetAnalysisRecordDetailItem) => c.role === 0,
      );
      const knowledgeInfo = data?.records.find(
        (c: API.Quality.GetAnalysisRecordDetailItem) => c.role === 1,
      );
      const thinkingInfo = data?.records.find(
        (c: API.Quality.GetAnalysisRecordDetailItem) => c.role === 2,
      );
      const answerInfo = data?.records.find(
        (c: API.Quality.GetAnalysisRecordDetailItem) => c.role === 3,
      );
      formRef.current?.setFieldsValue({
        desc: queryInfo?.content || '',
      });
      setKnowledge(knowledgeInfo?.content || '');
      setThinking(thinkingInfo?.content || '');
      setAnswer(answerInfo?.content || '');
      setDetailLoading(false);
    },
    [historyActions],
  );

  useMount(() => {
    if (query) {
      formRef.current?.setFieldsValue({
        desc: query as string,
      });
    }
    if (id) {
      onHandleSelect(id as string);
    }
  });

  const onHandleReference = useCallback(() => {
    referenceActions.setTrue();
  }, [referenceActions]);

  return (
    <div className="w-screen h-screen max-w-full max-h-full bg-[#F7F8FA] p-4 gap-x-4 flex">
      {contextHolder}
      <div className="rounded-[2px] bg-white h-full w-[30%] min-w-[320px] relative">
        <div
          className="absolute bottom-0 left-0 h-[48px] flex justify-center items-center px-[16px] gap-x-2 cursor-pointer"
          onClick={() => historyActions.setTrue()}
        >
          <ClockCircleOutlined className="text-[16px] translate-y-[1px]" />
          <span className="text-[14px] font-bold">历史记录</span>
        </div>
        <History
          open={historyVisible}
          key={historyActions.visibleKey}
          onClose={() => historyActions.setFalse()}
          onSelect={onHandleSelect}
        />
        <header className="border-b border-[#E8E8E8] flex items-center gap-2 h-[48px] px-4">
          <QuestionCircleFilled className="text-[#277FF4] text-[24px]" />
          <span className="text-[16px]">问题输入</span>
        </header>
        <div className="p-4">
          <Form layout="vertical" ref={formRef}>
            <Form.Item
              name="desc"
              label="问题描述"
              rules={[
                {
                  required: true,
                  message: '请输入问题描述',
                },
              ]}
            >
              <Input.TextArea
                placeholder="请输入"
                maxLength={2000}
                showCount
                rows={5}
              />
            </Form.Item>
            <Button
              icon={<SearchOutlined />}
              type="primary"
              loading={loading}
              onClick={onAnalyze}
            >
              智能分析
            </Button>
          </Form>
        </div>
      </div>
      <div className="rounded-[2px] bg-white h-full flex flex-col flex-1 min-w-0">
        <header className="border-b border-[#E8E8E8] flex items-center gap-2 h-[48px] px-4">
          <DashboardFilled className="text-[#277FF4] text-[24px]" />
          <span className="text-[16px]">智能问题辅助分析</span>
        </header>
        <div className="py-4 flex-1 min-h-0">
          <If condition={detailLoading}>
            <Then>
              <div className="h-full flex justify-center items-center">
                <Spin />
              </div>
            </Then>
            <Else>
              <div
                className="h-full flex flex-col gap-y-4 px-4 overflow-y-auto"
                ref={domRef}
                onScroll={onScroll}
              >
                <If condition={empty}>
                  <Then>
                    <div className="w-full flex flex-col items-center pt-[120px]">
                      <img
                        className="w-[300px] object-contain"
                        src={emptyImage}
                        alt="emptyImage"
                      />
                      <div className="text-[rgba(0,0,0,0.65)] text-[16px] mt-[6px]">
                        请输入问题开始辅助分析~
                      </div>
                    </div>
                  </Then>
                  <Else>
                    <Collapse
                      expandIconPosition="end"
                      items={[
                        {
                          key: 'default',
                          label: <span className="font-bold">知识库检索</span>,
                          children:
                            thinking || currentType >= 1 ? (
                              <div className="markdown-body think-body prose">
                                <Markdown
                                  remarkPlugins={[remarkGfm]}
                                  rehypePlugins={[rehypeRaw]}
                                >
                                  {knowledge}
                                </Markdown>
                              </div>
                            ) : (
                              <LoadingDots />
                            ),
                        },
                      ]}
                      defaultActiveKey={['default']}
                    />
                    <Collapse
                      expandIconPosition="end"
                      items={[
                        {
                          key: 'default',
                          label: <span className="font-bold">回答</span>,
                          children:
                            answer || currentType === 3 ? (
                              <div className="markdown-body prose">
                                <Markdown
                                  remarkPlugins={[remarkGfm]}
                                  rehypePlugins={[rehypeRaw]}
                                >
                                  {answer}
                                </Markdown>
                              </div>
                            ) : (
                              <LoadingDots />
                            ),
                        },
                      ]}
                      defaultActiveKey={['default']}
                    />
                    <Collapse
                      expandIconPosition="end"
                      items={[
                        {
                          key: 'default',
                          label: <span className="font-bold">参考文献</span>,
                          children:
                            reference.url || currentType === 7 ? (
                              <div className="flex flex-col gap-y-2 prose">
                                <div
                                  className="text-[14px] flex items-center gap-x-2 cursor-pointer"
                                  onClick={onHandleReference}
                                >
                                  <img
                                    src={PdfIcon}
                                    className="w-[24px] h-[24px]"
                                  ></img>
                                  <span className="text-[14px] text-[#277FF4]">
                                    {reference.fileName}
                                  </span>
                                </div>
                              </div>
                            ) : (
                              <LoadingDots />
                            ),
                        },
                      ]}
                      defaultActiveKey={['default']}
                    />
                  </Else>
                </If>
              </div>
            </Else>
          </If>
        </div>
        <Reference
          open={referenceVisible}
          onClose={() => referenceActions.setFalse()}
          reference={reference}
        />
      </div>
    </div>
  );
}
