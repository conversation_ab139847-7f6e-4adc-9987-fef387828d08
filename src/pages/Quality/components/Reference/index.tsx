import type { DrawerProps } from 'antd';
import { Drawer } from 'antd';
import React from 'react';

export interface IProps extends DrawerProps {
  open: boolean;
  onClose: () => void;
  reference: {
    url: string;
    fileName: string;
  };
}

function Reference(props: IProps) {
  const { open, onClose, reference } = props;
  const { url } = reference;
  return (
    <React.Fragment>
      <Drawer
        title="参考文献"
        placement="right"
        closable={false}
        onClose={onClose}
        open={open}
        width="60%"
      >
        <iframe src={url} className="w-full h-full"></iframe>
      </Drawer>
    </React.Fragment>
  );
}

export default Reference;
