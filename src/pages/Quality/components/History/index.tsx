import type { DrawerProps } from "antd";
import { Button, Drawer, List, message, Modal, Skeleton } from "antd";
import React, { useCallback, useMemo, useState } from "react";
import { useMount, usePagination } from "ahooks";
import qualityService from "@/services/qualityService";

export interface IProps extends DrawerProps {
  open: boolean;
  onClose: () => void;
  onSelect: (conversionId:string) => void;
}

function History(props: IProps) {
  const { open, onClose, onSelect } = props;

  const [list, setList] = useState<API.Quality.GetAnalysisRecordsItem[]>([]);

  const { loading, run, pagination } = usePagination(
    async (config) => {
      const { current, pageSize } = config;
      const result = await qualityService.getAnalysisRecords({
        page: current,
        size: pageSize,
      });
      return Promise.resolve({
        list: result.data.records,
        total: result.data.total,
      });
    },
    {
      defaultPageSize: 10,
      defaultCurrent: 1,
      manual: true,
      onSuccess: (res, [config]) => {
        const { current } = config
        if (current === 1) {
          setList(res.list)
        }
        else {
          setList((draft) => {
            return [
              ...draft,
              ...res.list,
            ]
          })
        }
      },
    }
  );
  

  const onHandleClear = useCallback(() => {
    Modal.confirm({
      title: "清空历史记录",
      content: "确认清空全部历史记录？",
      onOk: async () => {
        await qualityService.clearAnalysisRecord();
        run({
          current: 1,
          pageSize: 10,
        });
        message.success("清空成功");
      },
    });
  }, [run]);

  const onLoadMore = useCallback(() => {
    const newPage = pagination.current + 1;
    run({
      current: newPage,
      pageSize: pagination.pageSize,
    });
  }, [pagination, run]);

  const loadMore = useMemo(() => {
    return (
      <React.Fragment>
        {!loading && (
          <React.Fragment>
            {(list.length ?? 0) > (pagination.total ?? 0) ? (
              <div className="flex justify-center items-center mt-4">
                <Button onClick={onLoadMore}>加载更多</Button>
              </div>
            ) : (
              <div className="flex justify-center items-center mt-4 text-gray-500">
                没有更多了
              </div>
            )}
          </React.Fragment>
        )}
      </React.Fragment>
    );
  }, [list.length, loading, onLoadMore, pagination.total]);

  const onHandleDelete = useCallback(
    (item: API.Quality.GetAnalysisRecordsItem) => {
      Modal.confirm({
        title: "删除分析记录",
        content: "确认删除该分析记录？",
        onOk: async () => {
          await qualityService.deleteAnalysisRecord(item.conversionId);
          message.success("删除成功");
          run({
            current: 1,
            pageSize: 10,
          });
        },
      });
    },
    [run]
  );

  const onHandleSelect = useCallback(
      (conversionId:string) => {
      onSelect(conversionId);
    },
    [onSelect]
  );

  useMount(() => {
    if (open) {
      run({
        current: 1,
        pageSize: 10,
      });
    }
  });

  return (
    <React.Fragment>
      <Drawer
        title={
          <div className="flex justify-between items-center">
            <div className="font-bold">历史记录</div>
            <Button type="primary" onClick={onHandleClear}>
              清空
            </Button>
          </div>
        }
        placement="left"
        closable={false}
        onClose={onClose}
        open={open}
        width="30%"
      >
        {loading && !list ? (
          <div className="flex justify-center items-center mt-4">
            <Skeleton active />
          </div>
        ) : (
          <React.Fragment>
            <List
              loadMore={loadMore}
              loading={loading}
              dataSource={list}
              renderItem={(item) => (
                <List.Item className="w-full">
                  <div
                    className="w-full flex justify-between items-center cursor-pointer hover:bg-gray-100 p-2 rounded-md"
                    onClick={() => onHandleSelect(item.conversionId)}
                  >
                    <div className="flex items-center">
                      <div className="ml-2">
                        <div className="text-sm font-medium">{item.query}</div>
                        <div className="text-xs text-gray-500">
                          {item.createTime}
                        </div>
                      </div>
                    </div>
                    <div>
                      <Button
                        type="text"
                        variant="link"
                        color="primary"
                        onClick={(e) => {
                          e.stopPropagation();
                          onHandleDelete(item);
                        }}
                      >
                        删除
                      </Button>
                    </div>
                  </div>
                </List.Item>
              )}
            />
          </React.Fragment>
        )}
      </Drawer>
    </React.Fragment>
  );
}

export default History;
