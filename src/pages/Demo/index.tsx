// 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果

import React, { useState, useEffect } from 'react';
import { Input } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';

const App: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [generatedText, setGeneratedText] = useState('拥抱数字化转型: 企业创新发展战略研讨会');

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setLoading(false);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  return (
    <div className="min-h-screen w-full bg-gray-50 flex items-center justify-center">
      <div className="w-[600px] bg-white rounded-lg shadow-sm p-6">
        <div className="space-y-4">
          <Input
            placeholder="会议名称"
            className="text-base h-12 border border-gray-200 rounded-lg px-4"
          />
          
          <div className="mt-6 space-y-4">
            <div className="text-gray-800 text-base leading-relaxed">
              {generatedText}
            </div>
            
            {loading && (
              <div className="flex items-center justify-between text-sm mt-4">
                <div className="flex items-center space-x-2">
                  <LoadingOutlined className="text-purple-500" />
                  <span className="text-purple-500">AI 生成中...</span>
                </div>
                <div className="flex items-center space-x-1 text-gray-400">
                  <span>停止</span>
                  <span className="px-1.5 py-0.5 border border-gray-200 rounded text-xs">Esc</span>
                </div>
              </div>
            )}
            
            <div className="relative overflow-hidden h-1 mt-2">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-200 to-purple-500 animate-loading-bar" />
            </div>
          </div>
        </div>
      </div>
      
      <style>{`
        @keyframes loadingBar {
          0% {
            transform: translateX(-100%);
          }
          100% {
            transform: translateX(100%);
          }
        }
        
        .animate-loading-bar {
          animation: loadingBar 1.5s ease-in-out infinite;
        }
      `}</style>
    </div>
  );
};

export default App;

