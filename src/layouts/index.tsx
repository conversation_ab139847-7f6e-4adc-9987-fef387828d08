import useStore from '@/store';
import {
  StyleProvider,
  legacyLogicalPropertiesTransformer,
} from '@ant-design/cssinjs';
import { Outlet, useLocation } from '@umijs/max';
import { useMount } from 'ahooks';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import queryString from 'query-string';

function Layout() {
  
  const location = useLocation();
  const setUseInfo = useStore((state) => state.setUseInfo);

  // 路由传参
  const { sessionId,userId,securityLevel,userName } = queryString.parse(location.search) as {
    sessionId?: string;
    userId?: string;
    securityLevel?: string;
    userName?: string;
  };

  useMount(() => {
    if (userId && userName && securityLevel && sessionId) {
      setUseInfo({
        userId: userId as string,
        userName: userName as string,
        securityLevel: securityLevel as string,
        sessionId: sessionId as string,
      });
    }
  });

  return (
    <StyleProvider
      hashPriority="high"
      transformers={[legacyLogicalPropertiesTransformer]}
    >
      <ConfigProvider
        locale={zhCN}
        theme={{
          components: {
            Card: {
              headerHeightSM: 46,
              headerFontSizeSM: 15,
              colorTextHeading: '#4b5563',
            },
            Tabs: {
              fontSize: 16,
              colorBorderSecondary: 'rgba(0,0,0,0)',
            },
          },
        }}
      >
        <Outlet />
      </ConfigProvider>
    </StyleProvider>
  );
}

export default Layout;
