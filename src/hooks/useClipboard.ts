import Clipboard from 'clipboard';

// 自定义封装复制功能
const useClipboardHooks = () => {
  const toClipboard = (text: string, container?: HTMLElement) => {
    // Fixes IE by appending element to body
    const appendToBody = true;
    return new Promise((resolve, reject) => {
      const fakeEl = document.createElement('button');
      const clipboard = new Clipboard(fakeEl, {
        text: () => text,
        action: () => 'copy',
        container: container !== undefined ? container : document.body,
      });
      clipboard.on('success', (e) => {
        clipboard.destroy();
        resolve(e);
      });
      clipboard.on('error', (e) => {
        clipboard.destroy();
        reject(e);
      });
      // appendToBody fixes IE
      if (appendToBody) document.body.appendChild(fakeEl);
      // simulate click
      fakeEl.click();
      // remove from body if appended
      if (appendToBody) document.body.removeChild(fakeEl);
    });
  };

  const onCopyText = async (text: string) => {
    return new Promise((resolve, reject) => {
      toClipboard(text)
        .then((e) => {
          resolve(e);
        })
        .catch((e) => {
          reject(e);
        });
    });
  };

  return {
    onCopyText,
    toClipboard,
  };
};

export default useClipboardHooks;
