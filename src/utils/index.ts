import { CheckType } from '@/enum';
import { nanoid } from '@ant-design/pro-components';

interface ListItem {
  text: string;
  correction: string;
  start: number;
  end: number;
  type: CheckType;
}

export interface ReplaceTextResult {
  id: string;
  old: string;
  new?: string;
  real?: string;
  type: CheckType;
  text?:string; // 最终选用的文本
}

export function replaceText(
  originalText: string,
  list: ListItem[],
): ReplaceTextResult[] {
  const checkList = list.toSorted((a, b) => a.start - b.start);
  const textArr: ReplaceTextResult[] = [];
  let lastIndex = 0;
  checkList.forEach((item) => {
    if (item.start > lastIndex) {
      textArr.push({
        old: originalText.slice(lastIndex, item.start),
        type: CheckType.TYPO,
        id: nanoid(),
      });
    }
    textArr.push({
      old: item.correction,
      new: item.text,
      real: item.text,
      text: item.correction, // 默认选择检测后的文本
      type: item.type,
      id: nanoid(),
    });
    lastIndex = item.end;
  });
  if (lastIndex < originalText.length) {
    textArr.push({
      old: originalText.slice(lastIndex),
      type: CheckType.TYPO,
      id: nanoid(),
    });
  }
  return textArr;
}
