import { createJSONStorage, persist } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import { shallow } from 'zustand/shallow'
import { createWithEqualityFn } from 'zustand/traditional'

interface Store {
  useInfo: {
    userId: string;
    userName: string;
    securityLevel: string;
    sessionId: string;
  }
  setUseInfo: (useInfo: Store['useInfo']) => void
  getUseInfo: () => Store['useInfo']
}

const useStore = createWithEqualityFn<Store>()(
  persist(
    immer((set, get) => ({
      useInfo:{
        userId: '202506201001',
        userName: 'TestUser',
        securityLevel: "1",
        sessionId: "1",
      },
      setUseInfo: (useInfo: Store['useInfo']) => set({ useInfo }),
      getUseInfo: () => get().useInfo,
    })),
    {
      name: 'store',
      storage: createJSONStorage(() => localStorage),
    },
  ),
  shallow,
)

export default useStore
