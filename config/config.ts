import { defineConfig } from '@umijs/max';

const isDev = process.env.NODE_ENV === 'development';

export default defineConfig({
  publicPath: isDev ? '/' : './',
  antd: {},
  access: {},
  model: {},
  initialState: {},
  request: {
    dataField: 'data',
  },
  hash: true,
  routes: [
    {
      path: '/:id',
      component: 'Home',
    },
    {
      path:'/quality',
      component:'Quality'
    },
    {
      name: 'AI 生成',
      path: '/demo',
      component: './Demo',
    },
  ],
  targets:{
    chrome: 40
  },
  headScripts: [
    './polyfill.min.js',
    // "https://cdn.jsdelivr.net/npm/web-streams-polyfill@4.1.0/dist/polyfill.min.js"
  ],
  legacy: {
  },
  proxy: {
    '/baseApi': {
      target: 'http://vpn.hexinjingu.com:9020/',
      // target: "http://*************:9020/",
      // target: "https://edb6-104-234-4-138.ngrok-free.app",
      changeOrigin: true,
      // 支持https
      secure: true,
      pathRewrite: { '^/baseApi': '' },
      onProxyRes: (proxyRes: any) => {
        // 解决SSE流接口一次性返回问题
        // eslint-disable-next-line no-param-reassign
        proxyRes.headers['Cache-Control'] = 'no-transform'
      }
    },
  },
  npmClient: 'pnpm',
  tailwindcss: {},
  history: { type: 'hash' },
  // mfsu:false
});
