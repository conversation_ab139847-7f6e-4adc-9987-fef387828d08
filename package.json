{"private": true, "author": "和信金谷-殷浩玮 <<EMAIL>>", "scripts": {"dev": "max dev", "build": "max build", "build:test": "UMI_ENV=test max build", "build:prod-test": "UMI_ENV=prod-test max build", "format": "prettier --cache --write .", "prepare": "husky", "postinstall": "max setup", "setup": "max setup", "start": "npm run dev"}, "dependencies": {"@ant-design/cssinjs": "^1.23.0", "@ant-design/icons": "^5.0.1", "@ant-design/pro-components": "^2.4.4", "@ant-design/x": "^1.4.0", "@microsoft/fetch-event-source": "^2.0.1", "@tailwindcss/typography": "^0.5.16", "@umijs/max": "^4.4.6", "ahooks": "^3.8.4", "antd": "^5.4.0", "clipboard": "^2.0.11", "dayjs": "^1.11.13", "github-markdown-css": "^5.8.1", "immer": "^10.1.1", "js-md5": "^0.8.3", "lodash": "^4.17.21", "nanoid": "^5.1.3", "qs": "^6.14.0", "query-string": "^9.2.1", "react-custom-scrollbars": "^4.2.1", "react-draggable": "^4.4.6", "react-if": "^4.1.6", "react-markdown": "^10.1.0", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "zustand": "^5.0.5"}, "devDependencies": {"@types/lodash": "^4.17.16", "@types/qs": "^6.14.0", "@types/react": "^18.0.33", "@types/react-custom-scrollbars": "^4.0.13", "@types/react-dom": "^18.0.11", "@umijs/lint": "^4.4.11", "@umijs/plugins": "^4.4.11", "eslint": "8.33.0", "husky": "^9", "lint-staged": "^13.2.0", "prettier": "^2.8.7", "prettier-plugin-organize-imports": "^3.2.2", "prettier-plugin-packagejson": "^2.4.3", "stylelint": "^14", "tailwindcss": "^3", "typescript": "^5.0.3"}}