module.exports = {
  extends: require.resolve('@umijs/max/eslint'),
  rules: {
    "no-param-reassign": [
      "error",
      {
        props: true,
        ignorePropertyModificationsFor: ["draft"],
      },
    ],
    "@typescript-eslint/consistent-type-imports": ["error"],
    "@typescript-eslint/no-empty-interface": 0,
    "react-hooks/exhaustive-deps": "error", // Checks effect dependencies
  },
};
